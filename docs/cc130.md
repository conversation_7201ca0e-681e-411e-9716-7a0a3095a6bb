# CC130 Modbus 系統整合指南

## 概述
本文檔說明 CC130 數位水表透過 Modbus RTU 協議進行系統整合時的重要數值規格。

## 核心數值規格

### 1. 累計用量值 (總累計積算值)

#### 基本資訊
- **寄存器地址：** 0 (40001)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 4 bytes (2個寄存器)
- **數據型別：** 32-bit Unsigned Integer
- **格式：** F1 (4 bytes組合)

#### 詳細規格
```
寄存器配置：
- Register 0: 包含 Data1 (2 bytes)
- Register 1: 包含 Data2 (2 bytes)

數據組成：
- X1 = Data1 High byte
- X2 = Data1 Low byte  
- X3 = Data2 High byte
- X4 = Data2 Low byte

計算公式：
總累計積算值 = X2 + (X1×256) + (X4×256²) + (X3×256³)
```

#### Modbus 命令格式
```
發送 (Send):
Device Address | Function Code | Register Address | Length of Data | CRC
     [01]      |      [03]     |     [00][00]     |    [00][02]    | [CRC]

接收 (Receive):
Device Address | Function Code | Byte Count | Data1 | Data2 | CRC
     [01]      |      [03]     |    [04]    |[X1][X2]|[X3][X4]|[CRC]
```

#### 實際範例
```
發送: 01 03 00 00 00 02 C4 0B
接收: 01 03 04 7B 26 00 14 7A 9E

計算過程:
X1=123, X2=38, X3=0, X4=20
總累計積算值 = 38 + (123×256) + (20×256²) + (0×256³) = 1,342,246
```

### 2. 小數點位數

#### 基本資訊
- **寄存器地址：** 215 (40216)
- **Function Code：** 3 (Read Holding Registers)
- **數據長度：** 2 bytes (1個寄存器)
- **數據型別：** 8-bit Unsigned Integer
- **格式：** F2 (2 bytes組合，僅使用低位元組)

#### 詳細規格
```
數據組成：
- Y1 = High byte (忽略)
- Y2 = Low byte (小數點位數)

有效數據：
- 僅使用 Y2 (Low byte)
- Y1 (High byte) 可忽略

取值範圍：
- 一般為 0-6 位小數點
```

#### Modbus 命令格式
```
發送 (Send):
Device Address | Function Code | Register Address | Length of Data | CRC
     [01]      |      [03]     |     [00][0A]     |    [00][01]    | [CRC]

接收 (Receive):
Device Address | Function Code | Byte Count | Data | CRC
     [01]      |      [03]     |    [02]    |[Y1][Y2]|[CRC]
```

#### 實際範例
```
發送: 01 03 00 0A 00 01 A4 0A
接收: 01 03 02 00 05 78 14

解析結果:
Y1=0 (忽略), Y2=5 (小數點位數)
```

## 系統整合實作

### 完整讀取流程
1. **讀取累計用量值** (寄存器 0，長度 2)
2. **讀取小數點位數** (寄存器 10，長度 1)
3. **計算最終顯示值**

### 數據處理範例
```python
# 假設讀取結果
raw_value = 1342246        # 累計用量值
decimal_places = 5         # 小數點位數

# 計算最終數值
final_value = raw_value / (10 ** decimal_places)
print(f"水表讀數: {final_value:.{decimal_places}f} 度")
# 輸出: 水表讀數: 13.42246 度
```

### 程式設計建議

#### 數據型別
```c
// C/C++ 建議
uint32_t cumulative_value;    // 累計用量值
uint8_t decimal_places;       // 小數點位數
```

```python
# Python 建議
cumulative_value: int         # 累計用量值
decimal_places: int           # 小數點位數
```

#### 錯誤處理
- 檢查 CRC 校驗碼確保數據完整性
- 驗證小數點位數合理範圍 (0-6)
- 處理通訊逾時和重試機制

### 性能優化建議
1. **批次讀取：** 考慮一次讀取多個連續寄存器
2. **快取策略：** 小數點位數通常不變，可適當快取
3. **輪詢頻率：** 根據應用需求調整讀取頻率

## 注意事項
- 累計用量值為 32-bit 數值，注意數值溢出處理
- 小數點位數設定後通常不會變更
- 建議在系統啟動時先讀取小數點位數，後續主要讀取累計用量值
- 確保 Modbus 通訊參數 (波特率、停止位、校驗位) 正確設定

## 相關寄存器
本文檔專注於核心用量數據，CC130 還支援其他功能：
- 電力不足天數 (寄存器 2)
- 漏水天數 (寄存器 3)
- 產品資訊 (寄存器 250-281)
- 通訊地址設定 (寄存器 210)
- 通訊鮑率設定 (寄存器 211)

---
*本文檔基於 CC130 Modbus Reference-2.1c 版本* 