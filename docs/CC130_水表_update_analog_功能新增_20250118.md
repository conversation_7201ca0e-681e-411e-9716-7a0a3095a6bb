# CC130 水表 update_analog 功能新增

**修改時間：** 2025年1月18日

## 修改概述

在 C1130 水表模組中新增了 update_analog API 調用功能，無論小數點位數是否>-1，都會額外調用 update_analog API 來更新 text_value，以便在資料庫中儲存原始水表讀值和小數位數資訊，方便除錯。

## 修改檔案

### 1. waterMeterNodeCc130.hpp
- **位置：** `24dio/src/waterMeterNodeCc130.hpp`
- **修改內容：**
  - 新增 `updateAnalogMessage()` 函數宣告
  - 新增詳細的函數註解說明

### 2. waterMeterNodeCc130.cpp
- **位置：** `24dio/src/waterMeterNodeCc130.cpp`
- **修改內容：**
  - 新增 `updateAnalogMessage()` 函數實作
  - 修改 `set_data()` 函數中的邏輯，無論小數點位數是否>-1都會調用 updateAnalogMessage()
  - 改善了小數點位數無效時的處理邏輯

## 功能詳細說明

### updateAnalogMessage() 函數
- **功能：** 更新 analog 訊息到系統
- **調用頻率：** 每分鐘更新一次（與原有的 updateMessage 相同）
- **text_value 格式：** 「水表讀值_小數位數」
  - 例如：`123.45_2` 表示水表讀值為 123.45，小數位數為 2
  - 例如：`1234_-1` 表示水表讀值為 1234，小數位數無效（-1）

### API 調用
- **API 端點：** `/index.php?option="com_floor&task=sroots.update_analog"`
- **參數：**
  - `id`: 裝置 ID
  - `use_uint`: 設為 0（使用一般數值格式）
  - `text_value`: 格式化的文字值（水表讀值_小數位數）
  - `decimal_value`: 水表讀值（數值）

### 修改邏輯
1. **原有邏輯：** 只有當 `decimal_places > -1` 時才會處理累計用量值
2. **新邏輯：** 
   - 無論 `decimal_places` 是否有效，都會處理累計用量值
   - 當 `decimal_places > -1` 時，使用小數點位數計算最終數值
   - 當 `decimal_places = -1` 時，直接使用原始值
   - **無論哪種情況，都會調用 `updateAnalogMessage()`**

## 除錯優勢

1. **完整資訊保存：** text_value 中同時包含水表讀值和小數位數，方便追蹤問題
2. **狀態可視化：** 可以清楚看到小數點位數是否正常讀取
3. **資料完整性：** 即使小數點位數讀取失敗，仍會保存原始讀值
4. **除錯便利：** 透過 text_value 可以快速判斷是讀值問題還是小數點位數問題

## 範例輸出

### 正常情況
- **decimal_value:** 123.45
- **text_value:** "123.45_2"
- **說明：** 水表讀值 123.45，小數位數為 2

### 小數點位數無效情況
- **decimal_value:** 1234
- **text_value:** "1234_-1"
- **說明：** 原始讀值 1234，小數位數無效（-1）

## 相容性

- **向後相容：** 原有的 `update_watermeter` API 調用保持不變
- **新增功能：** 額外的 `update_analog` API 調用不會影響現有功能
- **資料庫：** 使用現有的 `text_value` 和 `decimal_value` 欄位，無需修改資料庫結構

## 修正記錄

### 第一次修正（2025年1月18日）
- **問題：** text_value 沒有正常更新到資料庫
- **原因分析：**
  1. 缺少 `use_uint=0` 參數
  2. 參數順序與成功範例不一致
  3. 時間戳檢查導致函數不會立即執行
- **修正內容：**
  1. 新增 `use_uint=0` 參數
  2. 調整參數順序：`id` → `use_uint` → `text_value` → `decimal_value`
  3. 移除時間戳檢查，確保 `updateAnalogMessage()` 立即執行

## 測試建議

1. **正常讀取測試：** 確認小數點位數正常時，text_value 格式正確
2. **異常處理測試：** 確認小數點位數無效時，仍會更新 analog 資訊
3. **API 調用測試：** 確認 update_analog API 正常被調用
4. **資料庫驗證：** 確認 text_value 和 decimal_value 正確儲存到資料庫
5. **日誌檢查：** 查看 console 輸出確認 "CC130 water meter analog update" 訊息
